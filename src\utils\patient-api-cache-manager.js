/**
 * 病人API缓存管理器
 * 防止重复API请求，提供智能缓存和请求去重功能
 */

class PatientApiCacheManager {
  constructor() {
    // 存储每个病人的API缓存数据
    this.apiCache = new Map()
    // 存储正在进行的请求Promise，防止并发重复请求
    this.pendingRequests = new Map()
    // 存储每个病人的初始化状态
    this.initializationStatus = new Map()
    // 请求计数器，用于性能监控
    this.requestCounter = new Map()
  }

  /**
   * 获取病人的缓存键
   * @param {string} patientId 病人ID
   * @param {string} apiType API类型
   * @returns {string} 缓存键
   */
  getCacheKey(patientId, apiType) {
    return `${patientId}_${apiType}`
  }

  /**
   * 获取请求键（用于防止并发重复请求）
   * @param {string} patientId 病人ID
   * @param {string} apiType API类型
   * @returns {string} 请求键
   */
  getRequestKey(patientId, apiType) {
    return `${patientId}_${apiType}_pending`
  }

  /**
   * 检查API数据是否已缓存
   * @param {string} patientId 病人ID
   * @param {string} apiType API类型
   * @returns {boolean} 是否已缓存
   */
  hasCache(patientId, apiType) {
    const cacheKey = this.getCacheKey(patientId, apiType)
    return this.apiCache.has(cacheKey)
  }

  /**
   * 获取缓存的API数据
   * @param {string} patientId 病人ID
   * @param {string} apiType API类型
   * @returns {any} 缓存数据
   */
  getCache(patientId, apiType) {
    const cacheKey = this.getCacheKey(patientId, apiType)
    const cached = this.apiCache.get(cacheKey)
    
    if (cached) {
      console.log(`🎯 使用缓存数据: 病人${patientId} - ${apiType}`)
      this.incrementRequestCounter(patientId, `${apiType}_cache_hit`)
    }
    
    return cached
  }

  /**
   * 设置API数据缓存
   * @param {string} patientId 病人ID
   * @param {string} apiType API类型
   * @param {any} data 数据
   */
  setCache(patientId, apiType, data) {
    const cacheKey = this.getCacheKey(patientId, apiType)
    this.apiCache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      patientId,
      apiType
    })
    
    console.log(`💾 缓存API数据: 病人${patientId} - ${apiType}`)
  }

  /**
   * 执行带缓存和去重的API请求
   * @param {string} patientId 病人ID
   * @param {string} apiType API类型
   * @param {Function} apiFunction API函数
   * @param {any} params API参数
   * @param {Object} options 选项
   * @returns {Promise} API结果
   */
  async executeApiWithCache(patientId, apiType, apiFunction, params = null, options = {}) {
    const { forceRefresh = false, timeout = 30000 } = options
    
    // 检查缓存
    if (!forceRefresh && this.hasCache(patientId, apiType)) {
      return this.getCache(patientId, apiType).data
    }

    const requestKey = this.getRequestKey(patientId, apiType)
    
    // 检查是否有正在进行的相同请求
    if (this.pendingRequests.has(requestKey)) {
      console.log(`⏳ 等待进行中的请求: 病人${patientId} - ${apiType}`)
      return await this.pendingRequests.get(requestKey)
    }

    // 创建新的请求Promise
    const requestPromise = this.createApiRequest(patientId, apiType, apiFunction, params, timeout)
    this.pendingRequests.set(requestKey, requestPromise)

    try {
      const result = await requestPromise
      
      // 缓存成功的结果
      if (result && (result.hasError === 0 || result.success)) {
        this.setCache(patientId, apiType, result)
      }
      
      return result
    } finally {
      // 清理pending请求
      this.pendingRequests.delete(requestKey)
    }
  }

  /**
   * 创建API请求
   * @param {string} patientId 病人ID
   * @param {string} apiType API类型
   * @param {Function} apiFunction API函数
   * @param {any} params API参数
   * @param {number} timeout 超时时间
   * @returns {Promise} API Promise
   */
  async createApiRequest(patientId, apiType, apiFunction, params, timeout) {
    console.log(`🚀 发起API请求: 病人${patientId} - ${apiType}`)
    this.incrementRequestCounter(patientId, `${apiType}_api_call`)

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`API请求超时: ${apiType}`)), timeout)
    })

    try {
      const result = await Promise.race([
        apiFunction(params),
        timeoutPromise
      ])
      
      console.log(`✅ API请求成功: 病人${patientId} - ${apiType}`)
      return result
    } catch (error) {
      console.error(`❌ API请求失败: 病人${patientId} - ${apiType}`, error)
      this.incrementRequestCounter(patientId, `${apiType}_api_error`)
      throw error
    }
  }

  /**
   * 检查病人是否已完全初始化
   * @param {string} patientId 病人ID
   * @returns {boolean} 是否已初始化
   */
  isPatientFullyInitialized(patientId) {
    const status = this.initializationStatus.get(patientId)
    return status && status.fullyInitialized
  }

  /**
   * 标记病人为已完全初始化
   * @param {string} patientId 病人ID
   */
  markPatientFullyInitialized(patientId) {
    this.initializationStatus.set(patientId, {
      fullyInitialized: true,
      timestamp: Date.now(),
      requiredApis: ['patientInit', 'isMyPatient', 'menuPermissions']
    })
    
    console.log(`🎉 病人${patientId}完全初始化完成`)
  }

  /**
   * 增加请求计数
   * @param {string} patientId 病人ID
   * @param {string} type 请求类型
   */
  incrementRequestCounter(patientId, type) {
    const key = `${patientId}_${type}`
    const current = this.requestCounter.get(key) || 0
    this.requestCounter.set(key, current + 1)
  }

  /**
   * 获取性能统计
   * @param {string} patientId 病人ID
   * @returns {Object} 性能统计
   */
  getPerformanceStats(patientId) {
    const stats = {}
    
    for (const [key, count] of this.requestCounter.entries()) {
      if (key.startsWith(patientId)) {
        const type = key.replace(`${patientId}_`, '')
        stats[type] = count
      }
    }
    
    return {
      patientId,
      stats,
      isFullyInitialized: this.isPatientFullyInitialized(patientId),
      cacheHitRate: this.calculateCacheHitRate(patientId)
    }
  }

  /**
   * 计算缓存命中率
   * @param {string} patientId 病人ID
   * @returns {number} 缓存命中率
   */
  calculateCacheHitRate(patientId) {
    const cacheHits = this.requestCounter.get(`${patientId}_cache_hit`) || 0
    const apiCalls = this.requestCounter.get(`${patientId}_api_call`) || 0
    const total = cacheHits + apiCalls
    
    return total > 0 ? (cacheHits / total * 100).toFixed(2) : 0
  }

  /**
   * 清理病人的所有缓存数据
   * @param {string} patientId 病人ID
   */
  clearPatientCache(patientId) {
    // 清理API缓存
    for (const [key] of this.apiCache.entries()) {
      if (key.startsWith(patientId)) {
        this.apiCache.delete(key)
      }
    }
    
    // 清理pending请求
    for (const [key] of this.pendingRequests.entries()) {
      if (key.startsWith(patientId)) {
        this.pendingRequests.delete(key)
      }
    }
    
    // 清理初始化状态
    this.initializationStatus.delete(patientId)
    
    // 清理请求计数
    for (const [key] of this.requestCounter.entries()) {
      if (key.startsWith(patientId)) {
        this.requestCounter.delete(key)
      }
    }
    
    console.log(`🧹 清理病人${patientId}的所有缓存数据`)
  }

  /**
   * 获取调试信息
   * @returns {Object} 调试信息
   */
  getDebugInfo() {
    return {
      cachedApis: Array.from(this.apiCache.keys()),
      pendingRequests: Array.from(this.pendingRequests.keys()),
      initializedPatients: Array.from(this.initializationStatus.keys()),
      totalCacheSize: this.apiCache.size,
      totalPendingRequests: this.pendingRequests.size
    }
  }
}

// 创建单例实例
const patientApiCacheManager = new PatientApiCacheManager()

export default patientApiCacheManager
