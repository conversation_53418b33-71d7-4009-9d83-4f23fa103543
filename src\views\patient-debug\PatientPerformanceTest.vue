<!--
  病人页面性能测试组件
  用于验证API缓存和性能优化效果
-->
<template>
  <div class="patient-performance-test">
    <el-card>
      <div slot="header">
        <span>病人页面性能测试</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          刷新数据
        </el-button>
      </div>

      <!-- 性能摘要 -->
      <el-row :gutter="20" class="summary-cards">
        <el-col :span="6">
          <el-card shadow="never" class="summary-card">
            <div class="summary-item">
              <div class="summary-value">{{ performanceReport.summary?.totalPageSwitches || 0 }}</div>
              <div class="summary-label">页面切换次数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="summary-card">
            <div class="summary-item">
              <div class="summary-value">{{ performanceReport.summary?.totalApiRequests || 0 }}</div>
              <div class="summary-label">API请求总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="summary-card">
            <div class="summary-item">
              <div class="summary-value">{{ performanceReport.summary?.cacheHitRate || '0%' }}</div>
              <div class="summary-label">缓存命中率</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="summary-card">
            <div class="summary-item">
              <div class="summary-value">{{ performanceReport.summary?.totalErrors || 0 }}</div>
              <div class="summary-label">错误数量</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 测试操作 -->
      <div class="test-section">
        <h3>性能测试操作</h3>
        <el-row :gutter="10">
          <el-col :span="4">
            <el-button @click="runCacheTest" type="primary" :loading="testRunning">
              缓存测试
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button @click="runSwitchTest" type="success" :loading="testRunning">
              切换测试
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button @click="runStressTest" type="warning" :loading="testRunning">
              压力测试
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button @click="clearMetrics" type="info">
              清理数据
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button @click="exportReport" type="default">
              导出报告
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 页面切换性能 -->
      <div class="test-section">
        <h3>页面切换性能</h3>
        <el-table :data="performanceReport.pageSwitches" border size="small">
          <el-table-column prop="from" label="源病人" width="120" />
          <el-table-column prop="to" label="目标病人" width="120" />
          <el-table-column prop="duration" label="切换耗时" width="120">
            <template slot-scope="scope">
              <el-tag :type="getDurationTagType(scope.row.duration)">
                {{ scope.row.duration }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="apiRequests" label="API请求" width="100" />
          <el-table-column prop="cacheHits" label="缓存命中" width="100" />
        </el-table>
      </div>

      <!-- API请求统计 -->
      <div class="test-section">
        <h3>API请求统计</h3>
        <el-table :data="performanceReport.apiRequests" border size="small">
          <el-table-column prop="patientId" label="病人ID" width="120" />
          <el-table-column prop="apiType" label="API类型" width="150" />
          <el-table-column prop="totalRequests" label="总请求数" width="100" />
          <el-table-column prop="cacheHits" label="缓存命中" width="100" />
          <el-table-column prop="cacheHitRate" label="命中率" width="100">
            <template slot-scope="scope">
              <el-tag :type="getCacheHitRateTagType(scope.row.cacheHitRate)">
                {{ scope.row.cacheHitRate }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="averageDuration" label="平均耗时" width="120" />
        </el-table>
      </div>

      <!-- 优化建议 -->
      <div class="test-section" v-if="performanceReport.recommendations?.length > 0">
        <h3>优化建议</h3>
        <div class="recommendations">
          <el-alert
            v-for="(rec, index) in performanceReport.recommendations"
            :key="index"
            :title="rec.message"
            :type="getRecommendationAlertType(rec.priority)"
            :description="`类型: ${rec.type} | 优先级: ${rec.priority}`"
            show-icon
            class="recommendation-item"
          />
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="test-section" v-if="testResults.length > 0">
        <h3>测试结果</h3>
        <div class="test-results">
          <div v-for="(result, index) in testResults" :key="index" class="test-result-item">
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.testName }}
            </el-tag>
            <span class="test-result-message">{{ result.message }}</span>
            <span class="test-result-time">{{ formatTime(result.timestamp) }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import patientPerformanceMonitor from '@/utils/patient-performance-monitor'
import patientApiCacheManager from '@/utils/patient-api-cache-manager'
import simplePatientManager from '@/utils/simple-patient-manager'

export default {
  name: 'PatientPerformanceTest',
  
  data() {
    return {
      performanceReport: {},
      testRunning: false,
      testResults: []
    }
  },

  mounted() {
    this.refreshData()
    // 定期刷新数据
    this.refreshTimer = setInterval(this.refreshData, 5000)
  },

  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },

  methods: {
    refreshData() {
      this.performanceReport = patientPerformanceMonitor.getPerformanceReport()
    },

    async runCacheTest() {
      this.testRunning = true
      const testName = '缓存功能测试'
      
      try {
        const testPatientId = 'test-cache-patient'
        
        // 第一次请求（应该调用API）
        const start1 = performance.now()
        await patientApiCacheManager.executeApiWithCache(
          testPatientId,
          'testApi',
          () => new Promise(resolve => setTimeout(() => resolve({ hasError: 0, data: 'test' }), 100))
        )
        const duration1 = performance.now() - start1
        
        // 第二次请求（应该使用缓存）
        const start2 = performance.now()
        await patientApiCacheManager.executeApiWithCache(
          testPatientId,
          'testApi',
          () => new Promise(resolve => setTimeout(() => resolve({ hasError: 0, data: 'test' }), 100))
        )
        const duration2 = performance.now() - start2
        
        const success = duration2 < duration1 / 2 // 缓存应该快很多
        this.addTestResult(testName, success, 
          `第一次: ${duration1.toFixed(2)}ms, 第二次: ${duration2.toFixed(2)}ms`)
        
      } catch (error) {
        this.addTestResult(testName, false, `测试失败: ${error.message}`)
      } finally {
        this.testRunning = false
        this.refreshData()
      }
    },

    async runSwitchTest() {
      this.testRunning = true
      const testName = '页面切换测试'
      
      try {
        const patients = ['patient-001', 'patient-002', 'patient-003']
        let totalSwitchTime = 0
        
        for (let i = 0; i < patients.length - 1; i++) {
          const fromPatient = patients[i]
          const toPatient = patients[i + 1]
          
          patientPerformanceMonitor.startPageSwitch(fromPatient, toPatient)
          
          // 模拟页面切换
          await new Promise(resolve => setTimeout(resolve, 100))
          
          patientPerformanceMonitor.endPageSwitch(fromPatient, toPatient)
        }
        
        this.addTestResult(testName, true, `完成${patients.length - 1}次页面切换测试`)
        
      } catch (error) {
        this.addTestResult(testName, false, `测试失败: ${error.message}`)
      } finally {
        this.testRunning = false
        this.refreshData()
      }
    },

    async runStressTest() {
      this.testRunning = true
      const testName = '压力测试'
      
      try {
        const patients = ['stress-patient-1', 'stress-patient-2', 'stress-patient-3']
        const promises = []
        
        // 并发请求测试
        for (const patientId of patients) {
          for (let i = 0; i < 5; i++) {
            promises.push(
              patientApiCacheManager.executeApiWithCache(
                patientId,
                `stressApi${i}`,
                () => new Promise(resolve => 
                  setTimeout(() => resolve({ hasError: 0, data: `stress-${i}` }), 50)
                )
              )
            )
          }
        }
        
        await Promise.all(promises)
        this.addTestResult(testName, true, `完成${promises.length}个并发请求测试`)
        
      } catch (error) {
        this.addTestResult(testName, false, `测试失败: ${error.message}`)
      } finally {
        this.testRunning = false
        this.refreshData()
      }
    },

    clearMetrics() {
      patientPerformanceMonitor.clearMetrics()
      this.testResults = []
      this.refreshData()
      this.$message.success('性能数据已清理')
    },

    exportReport() {
      const report = patientPerformanceMonitor.getPerformanceReport()
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `patient-performance-report-${new Date().toISOString().slice(0, 19)}.json`
      a.click()
      URL.revokeObjectURL(url)
    },

    addTestResult(testName, success, message) {
      this.testResults.unshift({
        testName,
        success,
        message,
        timestamp: new Date()
      })
      
      // 只保留最近10个结果
      if (this.testResults.length > 10) {
        this.testResults = this.testResults.slice(0, 10)
      }
    },

    getDurationTagType(duration) {
      if (duration === 'pending') return 'info'
      const ms = parseFloat(duration)
      if (ms < 500) return 'success'
      if (ms < 1000) return 'warning'
      return 'danger'
    },

    getCacheHitRateTagType(rate) {
      const percentage = parseFloat(rate)
      if (percentage >= 80) return 'success'
      if (percentage >= 60) return 'warning'
      return 'danger'
    },

    getRecommendationAlertType(priority) {
      switch (priority) {
        case 'HIGH': return 'error'
        case 'MEDIUM': return 'warning'
        case 'LOW': return 'info'
        default: return 'info'
      }
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    }
  }
}
</script>

<style scoped>
.patient-performance-test {
  padding: 20px;
}

.summary-cards {
  margin-bottom: 20px;
}

.summary-card {
  text-align: center;
}

.summary-item {
  padding: 10px;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.summary-label {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.recommendations {
  margin-top: 10px;
}

.recommendation-item {
  margin-bottom: 10px;
}

.test-results {
  margin-top: 10px;
}

.test-result-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.test-result-message {
  margin-left: 10px;
  flex: 1;
}

.test-result-time {
  font-size: 12px;
  color: #999;
}
</style>
