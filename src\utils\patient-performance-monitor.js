/**
 * 病人页面性能监控工具
 * 监控API请求次数、缓存命中率、页面切换性能等指标
 */

import patientApiCacheManager from './patient-api-cache-manager'
import simplePatientManager from './simple-patient-manager'

class PatientPerformanceMonitor {
  constructor() {
    this.metrics = {
      pageSwitch: new Map(), // 页面切换性能
      apiRequests: new Map(), // API请求统计
      cacheHits: new Map(),   // 缓存命中统计
      errors: []              // 错误记录
    }
    
    this.isEnabled = process.env.NODE_ENV === 'development'
    this.startTime = Date.now()
  }

  /**
   * 记录页面切换开始
   * @param {string} fromPatientId 源病人ID
   * @param {string} toPatientId 目标病人ID
   */
  startPageSwitch(fromPatientId, toPatientId) {
    if (!this.isEnabled) return

    const switchId = `${fromPatientId || 'none'}_to_${toPatientId}`
    this.metrics.pageSwitch.set(switchId, {
      fromPatientId,
      toPatientId,
      startTime: performance.now(),
      endTime: null,
      duration: null,
      apiRequestCount: 0,
      cacheHitCount: 0
    })

    console.log(`📊 开始监控页面切换: ${switchId}`)
  }

  /**
   * 记录页面切换结束
   * @param {string} fromPatientId 源病人ID
   * @param {string} toPatientId 目标病人ID
   */
  endPageSwitch(fromPatientId, toPatientId) {
    if (!this.isEnabled) return

    const switchId = `${fromPatientId || 'none'}_to_${toPatientId}`
    const switchMetric = this.metrics.pageSwitch.get(switchId)
    
    if (switchMetric) {
      switchMetric.endTime = performance.now()
      switchMetric.duration = switchMetric.endTime - switchMetric.startTime
      
      // 获取API请求统计
      const perfStats = patientApiCacheManager.getPerformanceStats(toPatientId)
      switchMetric.apiRequestCount = this.countApiRequests(perfStats.stats)
      switchMetric.cacheHitCount = this.countCacheHits(perfStats.stats)
      
      console.log(`📊 页面切换完成: ${switchId}, 耗时: ${switchMetric.duration.toFixed(2)}ms`)
      
      // 如果切换时间过长，记录警告
      if (switchMetric.duration > 1000) {
        this.recordError('SLOW_PAGE_SWITCH', `页面切换过慢: ${switchId}, 耗时: ${switchMetric.duration.toFixed(2)}ms`)
      }
    }
  }

  /**
   * 记录API请求
   * @param {string} patientId 病人ID
   * @param {string} apiType API类型
   * @param {boolean} fromCache 是否来自缓存
   * @param {number} duration 请求耗时
   */
  recordApiRequest(patientId, apiType, fromCache, duration = 0) {
    if (!this.isEnabled) return

    const key = `${patientId}_${apiType}`
    const existing = this.metrics.apiRequests.get(key) || {
      patientId,
      apiType,
      totalRequests: 0,
      cacheHits: 0,
      totalDuration: 0,
      averageDuration: 0
    }

    existing.totalRequests++
    if (fromCache) {
      existing.cacheHits++
    } else {
      existing.totalDuration += duration
      existing.averageDuration = existing.totalDuration / (existing.totalRequests - existing.cacheHits)
    }

    this.metrics.apiRequests.set(key, existing)
  }

  /**
   * 记录错误
   * @param {string} type 错误类型
   * @param {string} message 错误消息
   * @param {any} data 附加数据
   */
  recordError(type, message, data = null) {
    const error = {
      type,
      message,
      data,
      timestamp: new Date().toISOString(),
      patientId: simplePatientManager.getCurrentPatientId()
    }

    this.metrics.errors.push(error)
    console.error(`🚨 性能监控错误: ${type} - ${message}`, data)

    // 只保留最近50个错误
    if (this.metrics.errors.length > 50) {
      this.metrics.errors = this.metrics.errors.slice(-50)
    }
  }

  /**
   * 获取性能报告
   * @param {string} patientId 病人ID（可选）
   * @returns {Object} 性能报告
   */
  getPerformanceReport(patientId = null) {
    const report = {
      timestamp: new Date().toISOString(),
      monitoringDuration: Date.now() - this.startTime,
      summary: this.generateSummary(),
      pageSwitches: this.getPageSwitchMetrics(patientId),
      apiRequests: this.getApiRequestMetrics(patientId),
      cachePerformance: this.getCachePerformance(patientId),
      errors: this.getErrorMetrics(),
      recommendations: this.generateRecommendations()
    }

    return report
  }

  /**
   * 生成性能摘要
   * @returns {Object} 性能摘要
   */
  generateSummary() {
    const totalSwitches = this.metrics.pageSwitch.size
    const totalApiRequests = Array.from(this.metrics.apiRequests.values())
      .reduce((sum, metric) => sum + metric.totalRequests, 0)
    const totalCacheHits = Array.from(this.metrics.apiRequests.values())
      .reduce((sum, metric) => sum + metric.cacheHits, 0)
    
    const cacheHitRate = totalApiRequests > 0 ? (totalCacheHits / totalApiRequests * 100).toFixed(2) : 0
    
    return {
      totalPageSwitches: totalSwitches,
      totalApiRequests,
      totalCacheHits,
      cacheHitRate: `${cacheHitRate}%`,
      totalErrors: this.metrics.errors.length
    }
  }

  /**
   * 获取页面切换指标
   * @param {string} patientId 病人ID
   * @returns {Array} 页面切换指标
   */
  getPageSwitchMetrics(patientId) {
    const switches = Array.from(this.metrics.pageSwitch.values())
    
    if (patientId) {
      return switches.filter(s => s.toPatientId === patientId || s.fromPatientId === patientId)
    }
    
    return switches.map(s => ({
      from: s.fromPatientId,
      to: s.toPatientId,
      duration: s.duration ? `${s.duration.toFixed(2)}ms` : 'pending',
      apiRequests: s.apiRequestCount,
      cacheHits: s.cacheHitCount
    }))
  }

  /**
   * 获取API请求指标
   * @param {string} patientId 病人ID
   * @returns {Array} API请求指标
   */
  getApiRequestMetrics(patientId) {
    const requests = Array.from(this.metrics.apiRequests.values())
    
    if (patientId) {
      return requests.filter(r => r.patientId === patientId)
    }
    
    return requests.map(r => ({
      patientId: r.patientId,
      apiType: r.apiType,
      totalRequests: r.totalRequests,
      cacheHits: r.cacheHits,
      cacheHitRate: `${(r.cacheHits / r.totalRequests * 100).toFixed(2)}%`,
      averageDuration: r.averageDuration ? `${r.averageDuration.toFixed(2)}ms` : 'N/A'
    }))
  }

  /**
   * 获取缓存性能
   * @param {string} patientId 病人ID
   * @returns {Object} 缓存性能
   */
  getCachePerformance(patientId) {
    if (patientId) {
      return patientApiCacheManager.getPerformanceStats(patientId)
    }
    
    const cachedPatients = simplePatientManager.getCachedPatientIds()
    return cachedPatients.map(id => patientApiCacheManager.getPerformanceStats(id))
  }

  /**
   * 获取错误指标
   * @returns {Object} 错误指标
   */
  getErrorMetrics() {
    const errorsByType = {}
    this.metrics.errors.forEach(error => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1
    })

    return {
      totalErrors: this.metrics.errors.length,
      errorsByType,
      recentErrors: this.metrics.errors.slice(-10)
    }
  }

  /**
   * 生成性能优化建议
   * @returns {Array} 优化建议
   */
  generateRecommendations() {
    const recommendations = []
    const summary = this.generateSummary()

    // 缓存命中率建议
    const cacheHitRate = parseFloat(summary.cacheHitRate)
    if (cacheHitRate < 80) {
      recommendations.push({
        type: 'CACHE_OPTIMIZATION',
        priority: 'HIGH',
        message: `缓存命中率较低(${summary.cacheHitRate})，建议优化缓存策略`
      })
    }

    // 页面切换性能建议
    const slowSwitches = Array.from(this.metrics.pageSwitch.values())
      .filter(s => s.duration && s.duration > 1000)
    
    if (slowSwitches.length > 0) {
      recommendations.push({
        type: 'PAGE_SWITCH_OPTIMIZATION',
        priority: 'MEDIUM',
        message: `发现${slowSwitches.length}次慢速页面切换，建议优化加载逻辑`
      })
    }

    // 错误率建议
    if (this.metrics.errors.length > 10) {
      recommendations.push({
        type: 'ERROR_HANDLING',
        priority: 'HIGH',
        message: `错误数量较多(${this.metrics.errors.length})，建议检查错误处理逻辑`
      })
    }

    return recommendations
  }

  /**
   * 计算API请求数量
   * @param {Object} stats 统计数据
   * @returns {number} API请求数量
   */
  countApiRequests(stats) {
    return Object.keys(stats)
      .filter(key => key.endsWith('_api_call'))
      .reduce((sum, key) => sum + stats[key], 0)
  }

  /**
   * 计算缓存命中数量
   * @param {Object} stats 统计数据
   * @returns {number} 缓存命中数量
   */
  countCacheHits(stats) {
    return Object.keys(stats)
      .filter(key => key.endsWith('_cache_hit'))
      .reduce((sum, key) => sum + stats[key], 0)
  }

  /**
   * 清理监控数据
   */
  clearMetrics() {
    this.metrics.pageSwitch.clear()
    this.metrics.apiRequests.clear()
    this.metrics.cacheHits.clear()
    this.metrics.errors = []
    this.startTime = Date.now()
    
    console.log('🧹 性能监控数据已清理')
  }

  /**
   * 导出性能数据
   * @returns {Object} 导出数据
   */
  exportMetrics() {
    return {
      pageSwitch: Array.from(this.metrics.pageSwitch.entries()),
      apiRequests: Array.from(this.metrics.apiRequests.entries()),
      cacheHits: Array.from(this.metrics.cacheHits.entries()),
      errors: this.metrics.errors,
      timestamp: new Date().toISOString()
    }
  }
}

// 创建单例实例
const patientPerformanceMonitor = new PatientPerformanceMonitor()

// 在开发环境下暴露到全局
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.patientPerformanceMonitor = patientPerformanceMonitor
}

export default patientPerformanceMonitor
